// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  TRAINEE
  ADMIN
}

enum ModuleCategory {
  PHISHING
  PASSWORD_SECURITY
  SOCIAL_ENGINEERING
  DATA_PROTECTION
  SAFE_BROWSING
  INCIDENT_RESPONSE
}

enum DifficultyLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum ProgressStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
}

enum QuestionType {
  MULTIPLE_CHOICE
  TRUE_FALSE
  MULTIPLE_SELECT
}

enum CampaignStatus {
  DRAFT
  SCHEDULED
  ACTIVE
  COMPLETED
  CANCELLED
}

enum UserAction {
  EMAIL_OPENED
  LINK_CLICKED
  ATTACHMENT_DOWNLOADED
  CREDENTIALS_SUBMITTED
  REPORTED_PHISHING
  IGNORED
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  PHISHING_DETECTED
  TRAINING_REMINDER
  CAMPAIGN_SCHEDULED
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String
  password      String
  role          UserRole  @default(TRAINEE)
  avatar        String?
  isActive      Boolean   @default(true)
  emailVerified DateTime?
  lastLoginAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  progress           UserProgress[]
  campaignResults    CampaignResult[]
  notifications      Notification[]
  createdModules     TrainingModule[] @relation("ModuleCreator")
  createdCampaigns   PhishingCampaign[] @relation("CampaignCreator")
  createdTemplates   EmailTemplate[] @relation("TemplateCreator")
  badges             UserBadge[]
  sessions           Session[]

  @@map("users")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model TrainingModule {
  id                String          @id @default(cuid())
  title             String
  description       String
  content           String          @db.Text
  category          ModuleCategory
  difficulty        DifficultyLevel
  estimatedDuration Int             // in minutes
  isPublished       Boolean         @default(false)
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  createdBy         String

  // Relations
  creator  User           @relation("ModuleCreator", fields: [createdBy], references: [id])
  quiz     Quiz?
  progress UserProgress[]

  @@map("training_modules")
}

model Quiz {
  id           String @id @default(cuid())
  moduleId     String @unique
  title        String
  description  String?
  passingScore Int    @default(70)
  timeLimit    Int?   // in minutes
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  module    TrainingModule @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  questions QuizQuestion[]

  @@map("quizzes")
}

model QuizQuestion {
  id            String       @id @default(cuid())
  quizId        String
  question      String
  type          QuestionType
  options       String[]
  correctAnswer String[]
  explanation   String?
  points        Int          @default(1)
  order         Int
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  // Relations
  quiz Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("quiz_questions")
}

model UserProgress {
  id          String         @id @default(cuid())
  userId      String
  moduleId    String
  status      ProgressStatus @default(NOT_STARTED)
  score       Int?
  completedAt DateTime?
  timeSpent   Int            @default(0) // in minutes
  attempts    Int            @default(0)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  user   User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  module TrainingModule @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  @@unique([userId, moduleId])
  @@map("user_progress")
}

model EmailTemplate {
  id                 String   @id @default(cuid())
  name               String
  subject            String
  body               String   @db.Text
  senderName         String
  senderEmail        String
  attachments        String[]
  phishingIndicators String[]
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  createdBy          String

  // Relations
  creator   User               @relation("TemplateCreator", fields: [createdBy], references: [id])
  campaigns PhishingCampaign[]

  @@map("email_templates")
}

model PhishingCampaign {
  id             String         @id @default(cuid())
  name           String
  description    String?
  emailTemplateId String
  targetUsers    String[]
  scheduledAt    DateTime?
  status         CampaignStatus @default(DRAFT)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  createdBy      String

  // Relations
  creator       User             @relation("CampaignCreator", fields: [createdBy], references: [id])
  emailTemplate EmailTemplate    @relation(fields: [emailTemplateId], references: [id])
  results       CampaignResult[]

  @@map("phishing_campaigns")
}

model CampaignResult {
  id             String     @id @default(cuid())
  campaignId     String
  userId         String
  action         UserAction
  timestamp      DateTime   @default(now())
  ipAddress      String?
  userAgent      String?
  additionalData Json?

  // Relations
  campaign PhishingCampaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
  user     User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("campaign_results")
}

model Badge {
  id          String @id @default(cuid())
  name        String @unique
  description String
  icon        String
  criteria    Json   // Criteria for earning the badge
  points      Int    @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userBadges UserBadge[]

  @@map("badges")
}

model UserBadge {
  id       String @id @default(cuid())
  userId   String
  badgeId  String
  earnedAt DateTime @default(now())

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  badge Badge @relation(fields: [badgeId], references: [id], onDelete: Cascade)

  @@unique([userId, badgeId])
  @@map("user_badges")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  message   String
  type      NotificationType @default(INFO)
  isRead    Boolean          @default(false)
  actionUrl String?
  createdAt DateTime         @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model SystemSettings {
  id    String @id @default(cuid())
  key   String @unique
  value String
  updatedAt DateTime @updatedAt

  @@map("system_settings")
}
