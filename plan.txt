Phishing attacks remain a critical threat to organizations and individuals, exploiting human vulnerabilities and causing significant data breaches. This thesis presents the design and implementation of a Cybersecurity Awareness and Phishing Simulation Platform to address the urgent need for effective, interactive training tools. The platform aims to enhance users' ability to recognize and respond to phishing attempts through a combination of educational modules and realistic, multi-channel phishing simulations (email and SMS). Utilizing a web-based architecture with React.js for the frontend, Node.js for the backend, and PostgreSQL for data management, the system enables administrators to create customizable phishing campaigns, track user interactions, and visualize behavioral analytics in real-time. The methodology follows an agile development approach, incorporating user feedback and iterative testing to ensure usability and effectiveness. Evaluation with a sample user group demonstrates improved phishing detection rates and heightened cybersecurity awareness post-training. This work contributes a scalable, open-source-inspired solution to bolster organizational resilience against phishing threats, with potential for future enhancements like AI-driven adaptive simulations.