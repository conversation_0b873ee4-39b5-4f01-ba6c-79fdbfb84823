@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced Cyberpunk Styles */
@layer components {
  /* Glassmorphism Effects */
  .glass-cyber {
    background: rgba(224, 224, 224, 0.1);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.1);
  }

  .glass-cyber-dark {
    background: rgba(10, 10, 35, 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 212, 255, 0.2);
  }

  /* Neon Glow Effects */
  .neon-glow-blue {
    box-shadow:
      0 0 5px #00D4FF,
      0 0 10px #00D4FF,
      0 0 15px #00D4FF,
      0 0 20px #00D4FF;
  }

  .neon-glow-purple {
    box-shadow:
      0 0 5px #8B00FF,
      0 0 10px #8B00FF,
      0 0 15px #8B00FF,
      0 0 20px #8B00FF;
  }

  .neon-glow-pink {
    box-shadow:
      0 0 5px #FF007A,
      0 0 10px #FF007A,
      0 0 15px #FF007A,
      0 0 20px #FF007A;
  }

  .neon-glow-green {
    box-shadow:
      0 0 5px #00FF41,
      0 0 10px #00FF41,
      0 0 15px #00FF41,
      0 0 20px #00FF41;
  }

  .neon-glow-red {
    box-shadow:
      0 0 5px #FF073A,
      0 0 10px #FF073A,
      0 0 15px #FF073A,
      0 0 20px #FF073A;
  }

  /* Cyberpunk Buttons */
  .btn-cyber-primary {
    @apply relative px-6 py-3 font-semibold text-cyber-dark bg-gradient-to-r from-cyber-blue to-cyber-purple rounded-lg transition-all duration-300 hover:scale-105 overflow-hidden;
  }

  .btn-cyber-primary::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-cyber-blue to-cyber-purple opacity-0 transition-opacity duration-300;
    z-index: -1;
  }

  .btn-cyber-primary:hover::before {
    @apply opacity-20;
  }

  .btn-cyber-primary:hover {
    box-shadow:
      0 0 20px rgba(0, 212, 255, 0.5),
      0 0 40px rgba(139, 0, 255, 0.3);
  }

  /* Cyberpunk Inputs */
  .input-cyber {
    @apply w-full px-4 py-3 bg-transparent border-2 border-cyber-blue/30 rounded-lg text-cyber-white placeholder-cyber-white/50 transition-all duration-300;
    background: rgba(10, 10, 35, 0.5);
  }

  .input-cyber:focus {
    @apply border-cyber-blue outline-none;
    box-shadow:
      0 0 10px rgba(0, 212, 255, 0.3),
      inset 0 0 10px rgba(0, 212, 255, 0.1);
  }

  .input-cyber.error {
    @apply border-cyber-red;
    box-shadow:
      0 0 10px rgba(255, 7, 58, 0.3),
      inset 0 0 10px rgba(255, 7, 58, 0.1);
  }

  /* Animated Labels */
  .label-cyber {
    @apply absolute left-4 top-3 text-cyber-white/70 transition-all duration-300 pointer-events-none;
  }

  .label-cyber.active {
    @apply -top-2 left-2 text-xs bg-cyber-dark px-2 text-cyber-blue;
  }

  /* Text Gradients */
  .text-gradient-cyber {
    @apply bg-gradient-to-r from-cyber-blue via-cyber-purple to-cyber-pink bg-clip-text text-transparent;
  }

  .text-gradient-blue-purple {
    @apply bg-gradient-to-r from-cyber-blue to-cyber-purple bg-clip-text text-transparent;
  }

  /* Cyber Grid Background */
  .cyber-grid-bg {
    background-image:
      linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: cyber-grid 20s linear infinite;
  }

  /* Binary Rain Effect */
  .binary-rain {
    position: relative;
    overflow: hidden;
  }

  .binary-rain::before {
    content: '01001001 01101110 01110100 01100101 01101100 01101100 01101001 01100111 01100101 01101110 01100011 01100101';
    position: absolute;
    top: -100%;
    left: 0;
    right: 0;
    color: rgba(0, 212, 255, 0.1);
    font-family: 'JetBrains Mono', monospace;
    font-size: 12px;
    line-height: 20px;
    animation: matrix-rain 10s linear infinite;
    pointer-events: none;
    white-space: pre-wrap;
    word-break: break-all;
  }

  /* Floating Particles */
  .floating-particles {
    position: relative;
    overflow: hidden;
  }

  .floating-particles::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 50%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(139, 0, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(255, 0, 122, 0.1) 0%, transparent 50%);
    animation: particle-float 6s ease-in-out infinite;
    pointer-events: none;
  }

  /* Glitch Effect */
  .glitch-text {
    position: relative;
  }

  .glitch-text::before,
  .glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .glitch-text::before {
    animation: glitch-1 0.3s ease-in-out infinite;
    color: #FF073A;
    z-index: -1;
  }

  .glitch-text::after {
    animation: glitch-2 0.3s ease-in-out infinite;
    color: #00D4FF;
    z-index: -2;
  }
}

/* Scrollbar Styling */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(59, 130, 246, 0.3);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(59, 130, 246, 0.5);
  }
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  font-family: 'Inter', sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Enhanced Cyberpunk Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes cyber-grid {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(-50px) translateY(-50px); }
}

@keyframes matrix-rain {
  0% { transform: translateY(-100vh); }
  100% { transform: translateY(100vh); }
}

@keyframes particle-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(30px) rotate(240deg); }
}

@keyframes glitch-1 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

@keyframes glitch-2 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(2px, -2px); }
  40% { transform: translate(2px, 2px); }
  60% { transform: translate(-2px, -2px); }
  80% { transform: translate(-2px, 2px); }
}

@keyframes neon-pulse {
  0%, 100% {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor;
    opacity: 1;
  }
  50% {
    text-shadow:
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor;
    opacity: 0.8;
  }
}

/* Utility classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Custom component styles */
.card {
  @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
}

.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.btn-secondary {
  @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-800 font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
}

.btn-danger {
  @apply bg-danger-600 hover:bg-danger-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-danger-500 focus:ring-offset-2;
}

.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
}

.label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}
