# Database
DATABASE_URL="postgresql://username:password@localhost:5432/cybersecurity_platform"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# OAuth Providers (Optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Email Configuration (for notifications and password reset)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# JWT Secret
JWT_SECRET="your-jwt-secret-key"

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=900000

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_DIR="./uploads"

# Application Settings
APP_NAME="Cybersecurity Awareness Platform"
APP_URL="http://localhost:3000"
ADMIN_EMAIL="<EMAIL>"
